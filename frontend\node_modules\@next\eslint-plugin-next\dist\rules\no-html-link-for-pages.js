"use strict";
var _definerule = require("../utils/define-rule");
var _path = /*#__PURE__*/ _interop_require_wildcard(require("path"));
var _fs = /*#__PURE__*/ _interop_require_wildcard(require("fs"));
var _getrootdirs = require("../utils/get-root-dirs");
var _url = require("../utils/url");
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _array_without_holes(arr) {
    if (Array.isArray(arr)) return _array_like_to_array(arr);
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function _iterable_to_array(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _non_iterable_spread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _to_consumable_array(arr) {
    return _array_without_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_spread();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
var pagesDirWarning = (0, _url.execOnce)(function(pagesDirs) {
    console.warn("Pages directory cannot be found at ".concat(pagesDirs.join(' or '), ". ") + 'If using a custom path, please configure with the `no-html-link-for-pages` rule in your eslint config file.');
});
// Cache for fs.existsSync lookup.
// Prevent multiple blocking IO requests that have already been calculated.
var fsExistsSyncCache = {};
var memoize = function(fn) {
    var cache = {};
    return function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        var key = JSON.stringify(args);
        if (cache[key] === undefined) {
            cache[key] = fn.apply(void 0, _to_consumable_array(args));
        }
        return cache[key];
    };
};
var cachedGetUrlFromPagesDirectories = memoize(_url.getUrlFromPagesDirectories);
var cachedGetUrlFromAppDirectory = memoize(_url.getUrlFromAppDirectory);
var url = 'https://nextjs.org/docs/messages/no-html-link-for-pages';
module.exports = (0, _definerule.defineRule)({
    meta: {
        docs: {
            description: 'Prevent usage of `<a>` elements to navigate to internal Next.js pages.',
            category: 'HTML',
            recommended: true,
            url: url
        },
        type: 'problem',
        schema: [
            {
                oneOf: [
                    {
                        type: 'string'
                    },
                    {
                        type: 'array',
                        uniqueItems: true,
                        items: {
                            type: 'string'
                        }
                    }
                ]
            }
        ]
    },
    /**
   * Creates an ESLint rule listener.
   */ create: function create(context) {
        var ruleOptions = context.options;
        var _ruleOptions = _sliced_to_array(ruleOptions, 1), customPagesDirectory = _ruleOptions[0];
        var rootDirs = (0, _getrootdirs.getRootDirs)(context);
        var pagesDirs = (customPagesDirectory ? [
            customPagesDirectory
        ] : rootDirs.map(function(dir) {
            return [
                _path.join(dir, 'pages'),
                _path.join(dir, 'src', 'pages')
            ];
        })).flat();
        var foundPagesDirs = pagesDirs.filter(function(dir) {
            if (fsExistsSyncCache[dir] === undefined) {
                fsExistsSyncCache[dir] = _fs.existsSync(dir);
            }
            return fsExistsSyncCache[dir];
        });
        var appDirs = rootDirs.map(function(dir) {
            return [
                _path.join(dir, 'app'),
                _path.join(dir, 'src', 'app')
            ];
        }).flat();
        var foundAppDirs = appDirs.filter(function(dir) {
            if (fsExistsSyncCache[dir] === undefined) {
                fsExistsSyncCache[dir] = _fs.existsSync(dir);
            }
            return fsExistsSyncCache[dir];
        });
        // warn if there are no pages and app directories
        if (foundPagesDirs.length === 0 && foundAppDirs.length === 0) {
            pagesDirWarning(pagesDirs);
            return {};
        }
        var pageUrls = cachedGetUrlFromPagesDirectories('/', foundPagesDirs);
        var appDirUrls = cachedGetUrlFromAppDirectory('/', foundAppDirs);
        var allUrlRegex = _to_consumable_array(pageUrls).concat(_to_consumable_array(appDirUrls));
        return {
            JSXOpeningElement: function JSXOpeningElement(node) {
                if (node.name.name !== 'a') {
                    return;
                }
                if (node.attributes.length === 0) {
                    return;
                }
                var target = node.attributes.find(function(attr) {
                    return attr.type === 'JSXAttribute' && attr.name.name === 'target';
                });
                if (target && target.value.value === '_blank') {
                    return;
                }
                var href = node.attributes.find(function(attr) {
                    return attr.type === 'JSXAttribute' && attr.name.name === 'href';
                });
                if (!href || href.value && href.value.type !== 'Literal') {
                    return;
                }
                var hasDownloadAttr = node.attributes.find(function(attr) {
                    return attr.type === 'JSXAttribute' && attr.name.name === 'download';
                });
                if (hasDownloadAttr) {
                    return;
                }
                var hrefPath = (0, _url.normalizeURL)(href.value.value);
                // Outgoing links are ignored
                if (/^(https?:\/\/|\/\/)/.test(hrefPath)) {
                    return;
                }
                allUrlRegex.forEach(function(foundUrl) {
                    if (foundUrl.test((0, _url.normalizeURL)(hrefPath))) {
                        context.report({
                            node: node,
                            message: "Do not use an `<a>` element to navigate to `".concat(hrefPath, "`. Use `<Link />` from `next/link` instead. See: ").concat(url)
                        });
                    }
                });
            }
        };
    }
});
