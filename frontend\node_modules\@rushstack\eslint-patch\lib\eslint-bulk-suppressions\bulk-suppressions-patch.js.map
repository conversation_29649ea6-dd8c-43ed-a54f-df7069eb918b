{"version": 3, "file": "bulk-suppressions-patch.js", "sourceRoot": "", "sources": ["../../src/eslint-bulk-suppressions/bulk-suppressions-patch.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyI3D,gDAgCC;AAED,sBAiBC;AAED,sBASC;AAGD,8DAQC;AAED,gCAgBC;AAQD,oDA4BC;AArQD,4CAAoB;AAEpB,qDAAuC;AAEvC,gDAA8C;AAC9C,2CAIqB;AACrB,qEAOkC;AAElC,MAAM,kBAAkB,GAAa;IACnC,cAAc;IACd,eAAe;IACf,uGAAuG;IACvG,sDAAsD;CACvD,CAAC;AACF,MAAM,kBAAkB,GAAkB,MAAM,CAAC,aAAa,CAAC,CAAC;AAChE,MAAM,kCAAkC,GAAuB,OAAO,CAAC,GAAG,CAAC,6CAAiC,CAAC,CAAC;AAC9G,MAAM,kBAAkB,GAAY,kCAAkC,KAAK,GAAG,CAAC;AAC/E,MAAM,iBAAiB,GAA4B,kCAAkC;IACnF,CAAC,CAAC,IAAI,GAAG,CAAC,kCAAkC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxD,CAAC,CAAC,SAAS,CAAC;AAUd,SAAS,WAAW,CAAC,IAAmB;IACtC,IAAI,MAAM,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5C,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACtB,CAAC;SAAM,IAAI,MAAM,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC;QACtD,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACtB,CAAC;SAAM,IAAI,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC;QAClD,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACtB,CAAC;SAAM,IAAI,MAAM,CAAC,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC;QACrD,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACtB,CAAC;SAAM,IAAI,MAAM,CAAC,yDAAyD,CAAC,IAAI,CAAC,EAAE,CAAC;QAClF,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACtB,CAAC;SAAM,IAAI,MAAM,CAAC,qDAAqD,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9E,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IACvB,CAAC;SAAM,IAAI,MAAM,CAAC,8DAA8D,CAAC,IAAI,CAAC,EAAE,CAAC;QACvF,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IACvB,CAAC;SAAM,IAAI,MAAM,CAAC,wDAAwD,CAAC,IAAI,CAAC,EAAE,CAAC;QACjF,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACxB,CAAC;SAAM,IAAI,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IACvB,CAAC;SAAM,IAAI,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5C,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACtB,CAAC;SAAM,IAAI,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACtB,CAAC;SAAM,IAAI,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACtB,CAAC;AACH,CAAC;AAID,SAAS,gBAAgB,CAAC,IAAgC;IACxD,MAAM,QAAQ,GAAa,EAAE,CAAC;IAC9B,KAAK,IAAI,OAAO,GAA+B,IAAI,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QACvF,MAAM,iBAAiB,GAAuB,WAAW,CAAC,OAAO,CAAC,CAAC;QACnE,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACpC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO,GAAG,CAAC;IACb,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;AACH,CAAC;AAED,MAAM,8BAA8B,GAAwB,IAAI,GAAG,EAAE,CAAC;AAEtE,SAAS,mDAAmD,CAAC,kBAA0B;IACrF,MAAM,2BAA2B,GAC/B,8BAA8B,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IACzD,IAAI,2BAA2B,EAAE,CAAC;QAChC,OAAO,2BAA2B,CAAC;IACrC,CAAC;IACD,MAAM,wBAAwB,GAAW,kBAAkB,CAAC,SAAS,CACnE,CAAC,EACD,kBAAkB,CAAC,WAAW,CAAC,GAAG,CAAC,CACpC,CAAC;IAEF,MAAM,YAAY,GAAa,CAAC,kBAAkB,CAAC,CAAC;IACpD,IAAI,kBAAsC,CAAC;IAC3C,oBAAoB,EAAE,KACpB,IAAI,aAAa,GAAW,wBAAwB,EACpD,aAAa,EAAE,qCAAqC;KACpD,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAC1E,CAAC;QACD,MAAM,wBAAwB,GAAuB,8BAA8B,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACvG,IAAI,wBAAwB,EAAE,CAAC;YAC7B,wDAAwD;YACxD,kBAAkB,GAAG,wBAAwB,CAAC;YAC9C,MAAM;QACR,CAAC;QAED,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjC,KAAK,MAAM,gBAAgB,IAAI,kBAAkB,EAAE,CAAC;YAClD,IAAI,YAAE,CAAC,UAAU,CAAC,GAAG,aAAa,IAAI,gBAAgB,EAAE,CAAC,EAAE,CAAC;gBAC1D,kBAAkB,GAAG,aAAa,CAAC;gBACnC,MAAM,oBAAoB,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,kBAAkB,EAAE,CAAC;QACvB,KAAK,MAAM,aAAa,IAAI,YAAY,EAAE,CAAC;YACzC,8BAA8B,CAAC,GAAG,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;QACxE,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,kDAAkD,kBAAkB,EAAE,CAAC,CAAC;IAC1F,CAAC;AACH,CAAC;AAED,mHAAmH;AACnH,SAAgB,kBAAkB,CAAC,MAKlC;IACC,mGAAmG;IACnG,IAAI,OAAO,CAAC,GAAG,CAAC,2CAA+B,CAAC,KAAK,OAAO,EAAE,CAAC;QAC7D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;IAClF,MAAM,0BAA0B,GAAW,gBAAgB,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAChF,MAAM,iBAAiB,GACrB,mDAAmD,CAAC,0BAA0B,CAAC,CAAC;IAClF,MAAM,gBAAgB,GAAW,0BAA0B,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpG,MAAM,OAAO,GAAW,gBAAgB,CAAC,WAAW,CAAC,CAAC;IACtD,MAAM,WAAW,GAAiB,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAE5E,MAAM,MAAM,GAA4B,IAAA,mEAA0C,EAAC,iBAAiB,CAAC,CAAC;IACtG,MAAM,qBAAqB,GAAW,IAAA,6CAAoB,EAAC,WAAW,CAAC,CAAC;IACxE,MAAM,uBAAuB,GAAY,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAElG,IAAI,uBAAuB,IAAI,kBAAkB,KAAI,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,EAAE,CAAC;QAC9F,OAAO,CAAC,kBAAkB,CAAC,GAAG;YAC5B,WAAW;YACX,qBAAqB;YACrB,MAAM;SACP,CAAC;IACJ,CAAC;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,0CAA8B,CAAC,KAAK,GAAG,IAAI,uBAAuB,CAAC;AACxF,CAAC;AAED,SAAgB,KAAK;IACnB,KAAK,MAAM,CACT,kBAAkB,EAClB,kBAAkB,CACnB,IAAI,IAAA,0EAAiD,GAAE,EAAE,CAAC;QACzD,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,EAAE,yBAAyB,EAAE,aAAa,EAAE,GAAG,kBAAkB,CAAC;YACxE,MAAM,qBAAqB,GAA4B;gBACrD,sBAAsB,EAAE,yBAAyB;gBACjD,UAAU,EAAE,aAAa;gBACzB,yBAAyB,EAAE,IAAI,GAAG,EAAE;gBACpC,aAAa,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE;aACpC,CAAC;YAEF,IAAA,oDAA2B,EAAC,kBAAkB,EAAE,qBAAqB,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAgB,KAAK;IACnB,KAAK,MAAM,CACT,kBAAkB,EAClB,kBAAkB,CACnB,IAAI,IAAA,0EAAiD,GAAE,EAAE,CAAC;QACzD,IAAI,kBAAkB,EAAE,CAAC;YACvB,IAAA,oDAA2B,EAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;AACH,CAAC;AAED,+HAA+H;AAC/H,SAAgB,yBAAyB,CAAC,UAAkB;IAC1D,IAAI,CAAC,0BAAY,EAAE,CAAC;QAClB,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC;IAC7B,CAAC;IAED,MAAM,kBAAkB,GAAW,GAAG,0BAAY,aAAa,CAAC;IAChE,MAAM,kBAAkB,GAAW,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;IAChG,OAAO,OAAO,CAAC,kBAAkB,CAAC,CAAC;AACrC,CAAC;AAED,SAAgB,UAAU,CAAiB,aAA0B,EAAE,YAAyB;IAC9F,4DAA4D;IAC5D,MAAM,iBAAiB,GAAa,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAEvF,kCAAkC;IAClC,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE,CAAC;QACrC,8CAA8C;QAC9C,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;IAED,6BAA6B;IAC7B,KAAK,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;QAC1G,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC;YACrC,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAClC,UAAyE;IAEzE,OAAO,UAAyB,GAAG,IAAe;QAChD,MAAM,QAAQ,GAA2B,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,QAAQ,EAAE,CAAC;YACb,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,IAAI,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;oBAChC,MAAM,EACJ,qBAAqB,EACrB,WAAW,EACX,MAAM,EAAE,EACN,yBAAyB,EACzB,UAAU,EAAE,EAAE,YAAY,EAAE,EAC5B,aAAa,EAAE,EAAE,YAAY,EAAE,eAAe,EAAE,EACjD,EACF,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;oBAChC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,CAAC;wBAC1D,yBAAyB,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;wBACrD,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAClC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport type { TSESTree } from '@typescript-eslint/types';\nimport fs from 'fs';\n\nimport * as Guards from './ast-guards';\n\nimport { eslintFolder } from '../_patch-base';\nimport {\n  ESLINT_BULK_ENABLE_ENV_VAR_NAME,\n  ESLINT_BULK_PRUNE_ENV_VAR_NAME,\n  ESLINT_BULK_SUPPRESS_ENV_VAR_NAME\n} from './constants';\nimport {\n  getSuppressionsConfigForEslintrcFolderPath,\n  serializeSuppression,\n  type IBulkSuppressionsConfig,\n  type ISuppression,\n  writeSuppressionsJsonToFile,\n  getAllBulkSuppressionsConfigsByEslintrcFolderPath\n} from './bulk-suppressions-file';\n\nconst ESLINTRC_FILENAMES: string[] = [\n  '.eslintrc.js',\n  '.eslintrc.cjs'\n  // Several other filenames are allowed, but this patch requires that it be loaded via a JS config file,\n  // so we only need to check for the JS-based filenames\n];\nconst SUPPRESSION_SYMBOL: unique symbol = Symbol('suppression');\nconst ESLINT_BULK_SUPPRESS_ENV_VAR_VALUE: string | undefined = process.env[ESLINT_BULK_SUPPRESS_ENV_VAR_NAME];\nconst SUPPRESS_ALL_RULES: boolean = ESLINT_BULK_SUPPRESS_ENV_VAR_VALUE === '*';\nconst RULES_TO_SUPPRESS: Set<string> | undefined = ESLINT_BULK_SUPPRESS_ENV_VAR_VALUE\n  ? new Set(ESLINT_BULK_SUPPRESS_ENV_VAR_VALUE.split(','))\n  : undefined;\n\ninterface IProblem {\n  [SUPPRESSION_SYMBOL]?: {\n    config: IBulkSuppressionsConfig;\n    suppression: ISuppression;\n    serializedSuppression: string;\n  };\n}\n\nfunction getNodeName(node: TSESTree.Node): string | undefined {\n  if (Guards.isClassDeclarationWithName(node)) {\n    return node.id.name;\n  } else if (Guards.isFunctionDeclarationWithName(node)) {\n    return node.id.name;\n  } else if (Guards.isClassExpressionWithName(node)) {\n    return node.id.name;\n  } else if (Guards.isFunctionExpressionWithName(node)) {\n    return node.id.name;\n  } else if (Guards.isNormalVariableDeclaratorWithAnonymousExpressionAssigned(node)) {\n    return node.id.name;\n  } else if (Guards.isNormalObjectPropertyWithAnonymousExpressionAssigned(node)) {\n    return node.key.name;\n  } else if (Guards.isNormalClassPropertyDefinitionWithAnonymousExpressionAssigned(node)) {\n    return node.key.name;\n  } else if (Guards.isNormalAssignmentPatternWithAnonymousExpressionAssigned(node)) {\n    return node.left.name;\n  } else if (Guards.isNormalMethodDefinition(node)) {\n    return node.key.name;\n  } else if (Guards.isTSEnumDeclaration(node)) {\n    return node.id.name;\n  } else if (Guards.isTSInterfaceDeclaration(node)) {\n    return node.id.name;\n  } else if (Guards.isTSTypeAliasDeclaration(node)) {\n    return node.id.name;\n  }\n}\n\ntype NodeWithParent = TSESTree.Node & { parent?: TSESTree.Node };\n\nfunction calculateScopeId(node: NodeWithParent | undefined): string {\n  const scopeIds: string[] = [];\n  for (let current: NodeWithParent | undefined = node; current; current = current.parent) {\n    const scopeIdForASTNode: string | undefined = getNodeName(current);\n    if (scopeIdForASTNode !== undefined) {\n      scopeIds.unshift(scopeIdForASTNode);\n    }\n  }\n\n  if (scopeIds.length === 0) {\n    return '.';\n  } else {\n    return '.' + scopeIds.join('.');\n  }\n}\n\nconst eslintrcPathByFileOrFolderPath: Map<string, string> = new Map();\n\nfunction findEslintrcFolderPathForNormalizedFileAbsolutePath(normalizedFilePath: string): string {\n  const cachedFolderPathForFilePath: string | undefined =\n    eslintrcPathByFileOrFolderPath.get(normalizedFilePath);\n  if (cachedFolderPathForFilePath) {\n    return cachedFolderPathForFilePath;\n  }\n  const normalizedFileFolderPath: string = normalizedFilePath.substring(\n    0,\n    normalizedFilePath.lastIndexOf('/')\n  );\n\n  const pathsToCache: string[] = [normalizedFilePath];\n  let eslintrcFolderPath: string | undefined;\n  findEslintrcFileLoop: for (\n    let currentFolder: string = normalizedFileFolderPath;\n    currentFolder; // 'something'.substring(0, -1) is ''\n    currentFolder = currentFolder.substring(0, currentFolder.lastIndexOf('/'))\n  ) {\n    const cachedEslintrcFolderPath: string | undefined = eslintrcPathByFileOrFolderPath.get(currentFolder);\n    if (cachedEslintrcFolderPath) {\n      // Need to cache this result into the intermediate paths\n      eslintrcFolderPath = cachedEslintrcFolderPath;\n      break;\n    }\n\n    pathsToCache.push(currentFolder);\n    for (const eslintrcFilename of ESLINTRC_FILENAMES) {\n      if (fs.existsSync(`${currentFolder}/${eslintrcFilename}`)) {\n        eslintrcFolderPath = currentFolder;\n        break findEslintrcFileLoop;\n      }\n    }\n  }\n\n  if (eslintrcFolderPath) {\n    for (const checkedFolder of pathsToCache) {\n      eslintrcPathByFileOrFolderPath.set(checkedFolder, eslintrcFolderPath);\n    }\n\n    return eslintrcFolderPath;\n  } else {\n    throw new Error(`Cannot locate an ESLint configuration file for ${normalizedFilePath}`);\n  }\n}\n\n// One-line insert into the ruleContext report method to prematurely exit if the ESLint problem has been suppressed\nexport function shouldBulkSuppress(params: {\n  filename: string;\n  currentNode: TSESTree.Node;\n  ruleId: string;\n  problem: IProblem;\n}): boolean {\n  // Use this ENV variable to turn off eslint-bulk-suppressions functionality, default behavior is on\n  if (process.env[ESLINT_BULK_ENABLE_ENV_VAR_NAME] === 'false') {\n    return false;\n  }\n\n  const { filename: fileAbsolutePath, currentNode, ruleId: rule, problem } = params;\n  const normalizedFileAbsolutePath: string = fileAbsolutePath.replace(/\\\\/g, '/');\n  const eslintrcDirectory: string =\n    findEslintrcFolderPathForNormalizedFileAbsolutePath(normalizedFileAbsolutePath);\n  const fileRelativePath: string = normalizedFileAbsolutePath.substring(eslintrcDirectory.length + 1);\n  const scopeId: string = calculateScopeId(currentNode);\n  const suppression: ISuppression = { file: fileRelativePath, scopeId, rule };\n\n  const config: IBulkSuppressionsConfig = getSuppressionsConfigForEslintrcFolderPath(eslintrcDirectory);\n  const serializedSuppression: string = serializeSuppression(suppression);\n  const currentNodeIsSuppressed: boolean = config.serializedSuppressions.has(serializedSuppression);\n\n  if (currentNodeIsSuppressed || SUPPRESS_ALL_RULES || RULES_TO_SUPPRESS?.has(suppression.rule)) {\n    problem[SUPPRESSION_SYMBOL] = {\n      suppression,\n      serializedSuppression,\n      config\n    };\n  }\n\n  return process.env[ESLINT_BULK_PRUNE_ENV_VAR_NAME] !== '1' && currentNodeIsSuppressed;\n}\n\nexport function prune(): void {\n  for (const [\n    eslintrcFolderPath,\n    suppressionsConfig\n  ] of getAllBulkSuppressionsConfigsByEslintrcFolderPath()) {\n    if (suppressionsConfig) {\n      const { newSerializedSuppressions, newJsonObject } = suppressionsConfig;\n      const newSuppressionsConfig: IBulkSuppressionsConfig = {\n        serializedSuppressions: newSerializedSuppressions,\n        jsonObject: newJsonObject,\n        newSerializedSuppressions: new Set(),\n        newJsonObject: { suppressions: [] }\n      };\n\n      writeSuppressionsJsonToFile(eslintrcFolderPath, newSuppressionsConfig);\n    }\n  }\n}\n\nexport function write(): void {\n  for (const [\n    eslintrcFolderPath,\n    suppressionsConfig\n  ] of getAllBulkSuppressionsConfigsByEslintrcFolderPath()) {\n    if (suppressionsConfig) {\n      writeSuppressionsJsonToFile(eslintrcFolderPath, suppressionsConfig);\n    }\n  }\n}\n\n// utility function for linter-patch.js to make require statements that use relative paths in linter.js work in linter-patch.js\nexport function requireFromPathToLinterJS(importPath: string): import('eslint').Linter {\n  if (!eslintFolder) {\n    return require(importPath);\n  }\n\n  const pathToLinterFolder: string = `${eslintFolder}/lib/linter`;\n  const moduleAbsolutePath: string = require.resolve(importPath, { paths: [pathToLinterFolder] });\n  return require(moduleAbsolutePath);\n}\n\nexport function patchClass<T, U extends T>(originalClass: new () => T, patchedClass: new () => U): void {\n  // Get all the property names of the patched class prototype\n  const patchedProperties: string[] = Object.getOwnPropertyNames(patchedClass.prototype);\n\n  // Loop through all the properties\n  for (const prop of patchedProperties) {\n    // Override the property in the original class\n    originalClass.prototype[prop] = patchedClass.prototype[prop];\n  }\n\n  // Handle getters and setters\n  for (const [prop, descriptor] of Object.entries(Object.getOwnPropertyDescriptors(patchedClass.prototype))) {\n    if (descriptor.get || descriptor.set) {\n      Object.defineProperty(originalClass.prototype, prop, descriptor);\n    }\n  }\n}\n\n/**\n * This returns a wrapped version of the \"verify\" function from ESLint's Linter class\n * that postprocesses rule violations that weren't suppressed by comments. This postprocessing\n * records suppressions that weren't otherwise suppressed by comments to be used\n * by the \"suppress\" and \"prune\" commands.\n */\nexport function extendVerifyFunction(\n  originalFn: (this: unknown, ...args: unknown[]) => IProblem[] | undefined\n): (this: unknown, ...args: unknown[]) => IProblem[] | undefined {\n  return function (this: unknown, ...args: unknown[]): IProblem[] | undefined {\n    const problems: IProblem[] | undefined = originalFn.apply(this, args);\n    if (problems) {\n      for (const problem of problems) {\n        if (problem[SUPPRESSION_SYMBOL]) {\n          const {\n            serializedSuppression,\n            suppression,\n            config: {\n              newSerializedSuppressions,\n              jsonObject: { suppressions },\n              newJsonObject: { suppressions: newSuppressions }\n            }\n          } = problem[SUPPRESSION_SYMBOL];\n          if (!newSerializedSuppressions.has(serializedSuppression)) {\n            newSerializedSuppressions.add(serializedSuppression);\n            newSuppressions.push(suppression);\n            suppressions.push(suppression);\n          }\n        }\n      }\n    }\n\n    return problems;\n  };\n}\n"]}